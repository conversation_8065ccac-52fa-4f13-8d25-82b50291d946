import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON>, AfterViewInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { ThemeService } from '../services/theme.service';
import { AuthService } from '../core/auth/auth.service';
import { CreationAccount } from '../interfaces/creation-account';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-create-account',
  standalone: true,
  imports: [CommonModule ,FormsModule , RouterModule],
  templateUrl: './create-account.component.html',
  styleUrls: ['./create-account.component.css']
})
export class CreateAccountComponent implements OnInit, AfterViewInit, OnDestroy {
  accountData: CreationAccount = {
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    terms: false
  };
  showPassword = false;
  errorMessage = '';
  successMessage = '';
  role: string = '';
  isDarkMode = false;
  private destroy$ = new Subject<void>();

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private themeService: ThemeService,
    private authService: AuthService
  ) {
    this.route.queryParams.subscribe(params => {
      this.role = params['role'] || '';
    });
  }

  ngOnInit(): void {
    console.log('CreateAccountComponent ngOnInit - Initializing component');

    // Ensure proper layout initialization
    this.initializeLayout();

    // Subscribe to theme changes
    this.themeService.isDarkMode$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isDark => {
        this.isDarkMode = isDark;
      });
  }

  ngAfterViewInit(): void {
    console.log('CreateAccountComponent ngAfterViewInit - View initialized');

    // Force layout recalculation to prevent empty space issues
    setTimeout(() => {
      this.forceLayoutRecalculation();
    }, 0);
  }

  ngOnDestroy(): void {
    console.log('CreateAccountComponent ngOnDestroy - Cleaning up');
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeLayout(): void {
    // Ensure body and html have proper styles for create account page
    if (typeof document !== 'undefined') {
      document.body.style.margin = '0';
      document.body.style.padding = '0';
      document.body.style.overflow = 'hidden';
      document.documentElement.style.height = '100%';
      document.body.style.height = '100%';
    }
  }

  private forceLayoutRecalculation(): void {
    // Force browser to recalculate layout to prevent empty space
    if (typeof window !== 'undefined') {
      const container = document.querySelector('.container') as HTMLElement;
      if (container) {
        const currentDisplay = container.style.display;
        container.style.display = 'none';
        container.offsetHeight; // Trigger reflow
        container.style.display = currentDisplay || 'flex';
        console.log('Create account layout recalculation forced');
      }
    }
  }

  togglePassword() {
    this.showPassword = !this.showPassword;
  }

  onSubmit() {
    if (this.accountData.firstName && this.accountData.lastName &&
        this.accountData.email && this.accountData.password && this.accountData.terms) {

      this.authService.register(this.accountData, this.role).subscribe({
        next: (user) => {
          console.log('Account created successfully:', user);
          this.errorMessage = '';
          this.successMessage = 'Account registered successfully! Redirecting to complete your profile...';

          // Navigate to supplementary info page after 2 seconds to show success message
          setTimeout(() => {
            this.router.navigate(['/sup-info'], { queryParams: { role: this.role } });
          }, 2000);
        },
        error: (err) => {
          console.error('Registration error:', err);
          this.successMessage = '';
          this.errorMessage = err.message || 'An error occurred during registration. Please try again.';
        }
      });
    } else {
      this.errorMessage = 'Please fill in all required fields and accept the terms.';
    }
  }
}