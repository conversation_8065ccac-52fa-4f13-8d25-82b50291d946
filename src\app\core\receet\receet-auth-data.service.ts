import { Injectable, computed, signal, inject } from '@angular/core';
import { AuthService } from '../auth/auth.service';
import { 
  ReceetTransaction, 
  ReceetData, 
  ReceetState, 
  ReceetAnalytics,
  BrandAnalytics,
  PaymentMethodAnalytics,
  MonthlySpendingAnalytics,
  StatusAnalytics,
  parseTransactionAmount,
  calculateTransactionTotal,
  getTransactionsByUser,
  DEFAULT_RECEET_FILTERS,
  DEFAULT_RECEET_PAGINATION,
  isCollaboratingBrand
} from '../../interfaces/receet';

@Injectable({
  providedIn: 'root'
})
export class ReceetAuthDataService {
  private authService = inject(AuthService);

  // Private signals for internal state management
  private _allTransactions = signal<ReceetTransaction[]>([]);
  private _isLoading = signal<boolean>(false);
  private _error = signal<string | null>(null);
  private _lastSync = signal<Date | null>(null);

  // Public computed signals for reactive data access
  readonly isLoading = computed(() => this._isLoading());
  readonly error = computed(() => this._error());
  readonly lastSync = computed(() => this._lastSync());

  /**
   * Parse date string in DD/MM/YYYY format to Date object
   */
  private parseTransactionDate(dateStr: string): Date {
    const [day, month, year] = dateStr.split('/').map(num => parseInt(num, 10));
    return new Date(year, month - 1, day); // month is 0-indexed in Date constructor
  }

  /**
   * Sort transactions by date in descending order (newest first)
   */
  private sortTransactionsByDate(transactions: ReceetTransaction[]): ReceetTransaction[] {
    return transactions.sort((a, b) => {
      const dateA = this.parseTransactionDate(a.date);
      const dateB = this.parseTransactionDate(b.date);
      return dateB.getTime() - dateA.getTime(); // Descending order (newest first)
    });
  }

  // Main auth-based computed signal for user-specific transactions
  readonly userTransactions = computed((): ReceetTransaction[] => {
    const currentUser = this.authService.currentUser();
    const allTransactions = this._allTransactions();

    if (!currentUser?.email) {
      console.log('ReceetAuthDataService: No authenticated user, returning empty transactions');
      return [];
    }

    // Filter transactions for the current user
    const userTransactions = getTransactionsByUser(allTransactions, currentUser.email);
    console.log(`ReceetAuthDataService: Found ${userTransactions.length} transactions for user ${currentUser.email}`);

    // Sort transactions by date (newest first) before returning
    const sortedTransactions = this.sortTransactionsByDate([...userTransactions]);
    console.log(`ReceetAuthDataService: Sorted ${sortedTransactions.length} transactions by date (newest first)`);

    return sortedTransactions;
  });

  // Computed signal for receet data with user-specific information
  readonly receetData = computed((): ReceetData | null => {
    const currentUser = this.authService.currentUser();
    const transactions = this.userTransactions();
    
    if (!currentUser?.email) return null;

    const totalAmount = calculateTransactionTotal(transactions);
    
    return {
      id: `receet-${currentUser.email}`,
      userId: currentUser.email,
      transactions,
      totalTransactions: transactions.length,
      totalAmount,
      currency: 'TND',
      lastUpdated: new Date(),
      filters: { ...DEFAULT_RECEET_FILTERS },
      pagination: { 
        ...DEFAULT_RECEET_PAGINATION,
        totalEntries: transactions.length,
        totalPages: Math.ceil(transactions.length / DEFAULT_RECEET_PAGINATION.entriesPerPage)
      }
    };
  });

  // Computed signal for receet analytics
  readonly receetAnalytics = computed((): ReceetAnalytics | null => {
    const transactions = this.userTransactions();
    
    if (transactions.length === 0) return null;

    const totalSpent = calculateTransactionTotal(transactions);
    const averageTransactionAmount = totalSpent / transactions.length;

    // Calculate brand analytics
    const brandMap = new Map<string, { count: number; total: number; ratings: number[] }>();
    transactions.forEach(transaction => {
      const brand = transaction.brandName;
      const amount = parseTransactionAmount(transaction.amount);
      
      if (!brandMap.has(brand)) {
        brandMap.set(brand, { count: 0, total: 0, ratings: [] });
      }
      
      const brandData = brandMap.get(brand)!;
      brandData.count++;
      brandData.total += amount;
      brandData.ratings.push(transaction.rating);
    });

    const topBrands: BrandAnalytics[] = Array.from(brandMap.entries())
      .map(([brandName, data]) => ({
        brandName,
        transactionCount: data.count,
        totalAmount: data.total,
        percentage: (data.total / totalSpent) * 100,
        averageRating: data.ratings.reduce((sum, rating) => sum + rating, 0) / data.ratings.length
      }))
      .sort((a, b) => b.totalAmount - a.totalAmount)
      .slice(0, 10);

    // Calculate payment method breakdown
    const paymentMap = new Map<string, { count: number; total: number }>();
    transactions.forEach(transaction => {
      const payment = transaction.paymentMode;
      const amount = parseTransactionAmount(transaction.amount);
      
      if (!paymentMap.has(payment)) {
        paymentMap.set(payment, { count: 0, total: 0 });
      }
      
      const paymentData = paymentMap.get(payment)!;
      paymentData.count++;
      paymentData.total += amount;
    });

    const paymentMethodBreakdown: PaymentMethodAnalytics[] = Array.from(paymentMap.entries())
      .map(([paymentMode, data]) => ({
        paymentMode,
        transactionCount: data.count,
        totalAmount: data.total,
        percentage: (data.total / totalSpent) * 100
      }))
      .sort((a, b) => b.totalAmount - a.totalAmount);

    // Calculate monthly spending
    const monthlyMap = new Map<string, { total: number; count: number; year: number; month: string }>();
    transactions.forEach(transaction => {
      const date = new Date(transaction.date);
      const monthKey = `${date.getFullYear()}-${date.getMonth()}`;
      const amount = parseTransactionAmount(transaction.amount);
      
      if (!monthlyMap.has(monthKey)) {
        monthlyMap.set(monthKey, {
          total: 0,
          count: 0,
          year: date.getFullYear(),
          month: date.toLocaleDateString('en-US', { month: 'long' })
        });
      }
      
      const monthData = monthlyMap.get(monthKey)!;
      monthData.total += amount;
      monthData.count++;
    });

    const monthlySpending: MonthlySpendingAnalytics[] = Array.from(monthlyMap.entries())
      .map(([_, data]) => ({
        month: data.month,
        year: data.year,
        totalAmount: data.total,
        transactionCount: data.count,
        averageAmount: data.total / data.count
      }))
      .sort((a, b) => {
        if (a.year !== b.year) return b.year - a.year;
        return new Date(`${a.month} 1, ${a.year}`).getMonth() - new Date(`${b.month} 1, ${b.year}`).getMonth();
      });

    // Calculate status breakdown
    const statusMap = new Map<string, number>();
    transactions.forEach(transaction => {
      const status = transaction.status;
      statusMap.set(status, (statusMap.get(status) || 0) + 1);
    });

    const statusBreakdown: StatusAnalytics[] = Array.from(statusMap.entries())
      .map(([status, count]) => ({
        status,
        count,
        percentage: (count / transactions.length) * 100
      }))
      .sort((a, b) => b.count - a.count);

    return {
      totalSpent,
      transactionCount: transactions.length,
      averageTransactionAmount,
      topBrands,
      paymentMethodBreakdown,
      monthlySpending,
      statusBreakdown
    };
  });

  // Computed signal for receet state
  readonly receetState = computed((): ReceetState => {
    const receetData = this.receetData();
    const transactions = this.userTransactions();
    
    return {
      receetData,
      transactions,
      filteredTransactions: transactions, // Will be filtered by component
      displayedTransactions: [], // Will be set by component
      selectedTickets: new Set<number>(),
      isLoading: this._isLoading(),
      error: this._error(),
      lastSync: this._lastSync()
    };
  });

  constructor() {
    console.log('ReceetAuthDataService: Service initialized');
    this.loadInitialTransactions();
  }

  // Public methods for data management
  setTransactions(transactions: ReceetTransaction[]): void {
    console.log(`ReceetAuthDataService: Setting ${transactions.length} transactions`);
    // Sort transactions by date before setting them
    const sortedTransactions = this.sortTransactionsByDate([...transactions]);
    this._allTransactions.set(sortedTransactions);
    this._lastSync.set(new Date());

    // Auto-save to localStorage for all users
    this.saveAllTransactionsToStorage();
  }

  addTransaction(transaction: ReceetTransaction): void {
    const currentTransactions = this._allTransactions();
    const updatedTransactions = [...currentTransactions, transaction];
    // Sort all transactions by date after adding new one
    const sortedTransactions = this.sortTransactionsByDate(updatedTransactions);
    this.setTransactions(sortedTransactions);
    console.log(`ReceetAuthDataService: Added transaction ${transaction.ticketNumber} and sorted by date`);
  }

  updateTransaction(ticketNumber: number, updatedTransaction: Partial<ReceetTransaction>): void {
    const currentTransactions = this._allTransactions();
    const updatedTransactions = currentTransactions.map(transaction =>
      transaction.ticketNumber === ticketNumber
        ? { ...transaction, ...updatedTransaction }
        : transaction
    );
    this.setTransactions(updatedTransactions);
    console.log(`ReceetAuthDataService: Updated transaction ${ticketNumber}`);
  }

  removeTransaction(ticketNumber: number): void {
    const currentTransactions = this._allTransactions();
    const updatedTransactions = currentTransactions.filter(
      transaction => transaction.ticketNumber !== ticketNumber
    );
    this.setTransactions(updatedTransactions);
    console.log(`ReceetAuthDataService: Removed transaction ${ticketNumber}`);
  }

  refreshTransactions(): void {
    console.log('ReceetAuthDataService: Refreshing transactions');
    this.loadInitialTransactions();
  }

  setLoading(loading: boolean): void {
    this._isLoading.set(loading);
  }

  setError(error: string | null): void {
    this._error.set(error);
  }

  // Helper methods for component integration
  getTransactionsByBrand(brandName: string): ReceetTransaction[] {
    return this.userTransactions().filter(transaction => transaction.brandName === brandName);
  }

  getTransactionsByStatus(status: string): ReceetTransaction[] {
    return this.userTransactions().filter(transaction => transaction.status === status);
  }

  getTransactionsByPaymentMode(paymentMode: string): ReceetTransaction[] {
    return this.userTransactions().filter(transaction => transaction.paymentMode === paymentMode);
  }

  // localStorage integration methods
  private saveAllTransactionsToStorage(): void {
    try {
      const allTransactions = this._allTransactions();

      // Group transactions by userId and save separately
      const transactionsByUser = new Map<string, ReceetTransaction[]>();

      allTransactions.forEach(transaction => {
        if (!transactionsByUser.has(transaction.userId)) {
          transactionsByUser.set(transaction.userId, []);
        }
        transactionsByUser.get(transaction.userId)!.push(transaction);
      });

      // Save each user's transactions to their own localStorage key
      transactionsByUser.forEach((userTransactions, userId) => {
        const storageKey = `receet_transactions_${userId}`;
        localStorage.setItem(storageKey, JSON.stringify(userTransactions));
        console.log(`Saved ${userTransactions.length} transactions for user ${userId}`);
      });
    } catch (error) {
      console.error('Error saving transactions to localStorage:', error);
    }
  }

  private loadAllTransactionsFromStorage(): ReceetTransaction[] {
    try {
      const allTransactions: ReceetTransaction[] = [];

      // Get all localStorage keys that match our pattern
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('receet_transactions_')) {
          const storedData = localStorage.getItem(key);
          if (storedData) {
            const userTransactions = JSON.parse(storedData);
            allTransactions.push(...userTransactions);
          }
        }
      }

      console.log(`Loaded ${allTransactions.length} total transactions from localStorage`);
      // Sort all loaded transactions by date (newest first)
      const sortedTransactions = this.sortTransactionsByDate(allTransactions);
      console.log(`Sorted ${sortedTransactions.length} loaded transactions by date`);
      return sortedTransactions;
    } catch (error) {
      console.error('Error loading transactions from localStorage:', error);
      return [];
    }
  }

  private loadInitialTransactions(): void {
    console.log('ReceetAuthDataService: Loading initial transactions');
    this._isLoading.set(true);

    // Load transactions from localStorage
    const storedTransactions = this.loadAllTransactionsFromStorage();
    this._allTransactions.set(storedTransactions);

    // Simulate loading delay
    setTimeout(() => {
      this._isLoading.set(false);
      this._lastSync.set(new Date());
    }, 100);
  }
}
