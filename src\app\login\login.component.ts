import { Component, OnInit, On<PERSON><PERSON>roy, AfterViewInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { AuthService } from '../core/auth/auth.service';
import { ToastService } from '../services/toast.service';
import { Login } from '../interfaces/login';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent implements OnInit, AfterViewInit, OnDestroy {
  loginData: Login = {
    email: '',
    password: ''
  };
  showPassword = false;
  errorMessage = '';
  role: string = '';
  private destroy$ = new Subject<void>();

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private authService: AuthService,
    private toastService: ToastService
  ) {}

  ngOnInit() {
    console.log('LoginComponent ngOnInit - Initializing component');

    // Ensure proper layout initialization
    this.initializeLayout();

    this.route.queryParams
      .pipe(takeUntil(this.destroy$))
      .subscribe(params => {
        this.role = params['role'] || '';
        console.log('Login component role set to:', this.role);
      });
  }

  ngAfterViewInit() {
    console.log('LoginComponent ngAfterViewInit - View initialized');

    // Force layout recalculation to prevent empty space issues
    setTimeout(() => {
      this.forceLayoutRecalculation();
    }, 0);
  }

  ngOnDestroy() {
    console.log('LoginComponent ngOnDestroy - Cleaning up');
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeLayout() {
    // Ensure body and html have proper styles for login page
    if (typeof document !== 'undefined') {
      document.body.style.margin = '0';
      document.body.style.padding = '0';
      document.body.style.overflow = 'hidden';
      document.documentElement.style.height = '100%';
      document.body.style.height = '100%';
    }
  }

  private forceLayoutRecalculation() {
    // Force browser to recalculate layout to prevent empty space
    if (typeof window !== 'undefined') {
      const container = document.querySelector('.container') as HTMLElement;
      if (container) {
        const currentDisplay = container.style.display;
        container.style.display = 'none';
        container.offsetHeight; // Trigger reflow
        container.style.display = currentDisplay || 'flex';
        console.log('Login layout recalculation forced');
      }
    }
  }

  togglePassword() {
    this.showPassword = !this.showPassword;
  }

  onLogin() {
    if (this.loginData.email && this.loginData.password) {
      this.authService.login(this.loginData).subscribe({
        next: (user) => {
          console.log('Login successful:', user);
          this.errorMessage = '';

          // Show success toast notification
          const userName = user.full_name || user.email;
          this.toastService.showSuccess(
            'Login Successful',
            `Welcome back, ${userName}! You have been successfully logged in.`
          );

          // Navigate to the appropriate dashboard based on role
          const userRole = user.role || this.role;
          if (userRole === 'shopper') {
            this.router.navigate(['/shopper-dashboard']);
          } else if (userRole === 'seller') {
            this.router.navigate(['/seller/dashboard']);
          } else {
            // Default to shopper dashboard for unknown roles
            this.router.navigate(['/shopper-dashboard']);
          }
        },
        error: (err) => {
          console.error('Login error:', err);
          this.errorMessage = err.message || 'An error occurred during login. Please try again.';
        }
      });
    } else {
      this.errorMessage = 'Please fill in all required fields.';
    }
  }
}