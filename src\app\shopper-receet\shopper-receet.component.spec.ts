import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { TopNavbarComponent } from '../top-navbar/top-navbar.component';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { ShopperReceetComponent } from './shopper-receet.component';

describe('ShopperReceetComponent', () => {
  let component: ShopperReceetComponent;
  let fixture: ComponentFixture<ShopperReceetComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ShopperReceetComponent,
        FormsModule,
        TopNavbarComponent,
        SidebarComponent
      ]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(ShopperReceetComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should parse date strings correctly', () => {
    // Test the private parseTransactionDate method
    const testDate = '15/06/2022'; // DD/MM/YYYY format
    const parsedDate = (component as any).parseTransactionDate(testDate);

    expect(parsedDate).toBeInstanceOf(Date);
    expect(parsedDate.getFullYear()).toBe(2022);
    expect(parsedDate.getMonth()).toBe(5); // Month is 0-indexed (June = 5)
    expect(parsedDate.getDate()).toBe(15);
  });

  it('should sort transactions by date in descending order (newest first)', () => {
    // Create test transactions with different dates
    const testTransactions = [
      { ticketNumber: 1, date: '13/05/2022', productName: 'Old Product' },
      { ticketNumber: 2, date: '15/06/2023', productName: 'New Product' },
      { ticketNumber: 3, date: '01/01/2023', productName: 'Middle Product' }
    ];

    // Sort using the component's sorting method
    const sortedTransactions = (component as any).sortTransactionsByDate([...testTransactions]);

    // Verify the order: newest first (2023-06-15, 2023-01-01, 2022-05-13)
    expect(sortedTransactions[0].ticketNumber).toBe(2); // 15/06/2023
    expect(sortedTransactions[1].ticketNumber).toBe(3); // 01/01/2023
    expect(sortedTransactions[2].ticketNumber).toBe(1); // 13/05/2022
  });

  it('should maintain sorting after filtering transactions', () => {
    // Set up test data
    component.transactions = [
      { ticketNumber: 1, date: '13/05/2022', productName: 'Hat', brandName: 'Carrefour' },
      { ticketNumber: 2, date: '15/06/2023', productName: 'Laptop', brandName: 'Zen' },
      { ticketNumber: 3, date: '01/01/2023', productName: 'Mouse', brandName: 'Carrefour' }
    ];

    // Apply filtering (no search query, should include all)
    component.searchQuery = '';
    component.filterTransactions();

    // Verify that filtered transactions are sorted by date (newest first)
    expect(component.filteredTransactions[0].ticketNumber).toBe(2); // 15/06/2023
    expect(component.filteredTransactions[1].ticketNumber).toBe(3); // 01/01/2023
    expect(component.filteredTransactions[2].ticketNumber).toBe(1); // 13/05/2022
  });
});