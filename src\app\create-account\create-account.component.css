/* Reset default styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Ensure proper viewport handling and prevent empty space */
:host {
  display: block !important;
  width: 100% !important;
  height: 100vh !important;
  min-height: 100vh !important;
  overflow: hidden !important;
  position: relative !important;
  box-sizing: border-box !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Prevent any global styles from causing spacing issues */
:host * {
  box-sizing: border-box;
}

/* Ensure no unexpected margins or padding from global styles */
:host::before,
:host::after {
  display: none !important;
}

.container {
  display: flex;
  width: 100%;
  height: 100vh;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
  font-family: Arial, sans-serif;
  position: relative;
  box-sizing: border-box;
  background-color: #ffffff;
}

:host ::ng-deep html,
:host ::ng-deep body {
  overflow: visible !important;
  height: auto !important;
  min-height: 100vh !important;
}

/* Ensure container takes full viewport */
:host .container {
  height: 100vh;
  min-height: 100vh;
  overflow: hidden;
  position: relative;
  width: 100%;
}

/* Prevent global right-section styles from interfering */
:host .right-section {
  background: none !important;
  padding: 0 !important;
  border-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  justify-content: center !important;
  align-items: center !important;
}

.create-account-container {
  display: flex;
  width: 100vw;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.left-section {
  flex: 0 0 50%;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px;
  margin: 0;
  height: 100vh;
  min-height: 100vh;
  overflow-y: auto;
  box-sizing: border-box;
  position: relative;
}

.right-section {
  flex: 1;
  background: none !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
  padding: 0 !important;
  margin: 0 !important;
  border-radius: 0 !important;
  position: relative !important;
  overflow: hidden !important;
  height: 100vh !important;
  min-height: 100vh !important;
  max-height: 100vh !important;
  width: 50vw !important;
  flex-shrink: 0 !important;
  box-sizing: border-box !important;
}

.back-link {
  position: absolute;
  top: 20px;
  left: 20px;
  color: #666;
  text-decoration: none;
  font-size: 0.9rem;
}

h1 {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 10px;
}

p {
  margin-bottom: 20px;
  color: #666;
  font-size: 0.9rem;
}

p a {
  color: #6b48ff;
  text-decoration: none;
}

form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
  max-width: 400px; /* Match the form width to the screenshot */
}

.name-fields {
  display: flex;
  gap: 15px;
}

input {
  padding: 12px;
  border: 1px solid #ccc;
  border-radius: 20px;
  font-size: 1rem;
  width: 100%;
  box-sizing: border-box;
}

.password-field {
  position: relative;
}

.toggle-password {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: #666;
  font-size: 1rem;
}

.terms {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.9rem;
  color: #666;
}

.terms label a {
  color: #6b48ff;
  text-decoration: none;
}

.create-account-btn {
  padding: 12px;
  background: #6b48ff;
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  text-transform: uppercase;
  transition: background 0.3s;
  width: 100%; /* Ensure the button takes the full width of the form */
}

.create-account-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.divider {
  text-align: center;
  margin: 20px 0;
  color: #666;
  font-size: 0.9rem;
  position: relative;
  width: 100%;
  max-width: 400px; /* Match the form width */
}

.divider::before,
.divider::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 40%;
  height: 1px;
  background: #ccc;
}

.divider::before {
  left: 0;
}

.divider::after {
  right: 0;
}

.third-party-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
  max-width: 400px;
  align-items: center;
}

.third-party.google {
  margin-bottom: 15px;
}

.third-party {
  padding: 8px 16px;
  background: white;
  color: black;
  border: 1px solid #ccc;
  border-radius: 20px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  width: 100%;
  max-width: 400px;
  height: 40px;
}

/* Google icon styling is now handled in the global styles.css */

/* Apple icon styling is now handled in the global styles.css */

.error-message {
  color: red;
  margin-top: 10px;
  font-size: 0.9rem;
}

footer {
  margin-top: 20px;
  color: #666;
  font-size: 0.8rem;
}

/* Logo styling - covers entire right section */
.right-section {
  flex: 1 !important;
  background: none !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
  padding: 0 !important;
  margin: 0 !important;
  border-radius: 0 !important;
  position: relative !important;
  overflow: hidden !important;
  height: 100vh !important;
  min-height: 100vh !important;
  max-height: 100vh !important;
  width: 50vw !important;
  flex-shrink: 0 !important;
  box-sizing: border-box !important;
}

.receeto-logo {
  width: 100% !important;
  height: 100% !important;
  background-image: url('/assets/images/Recetto-Login_Register-Logo.png');
  background-size: 100% 100% !important;
  background-position: center center !important;
  background-repeat: no-repeat !important;
  display: block !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 1 !important;
  box-sizing: border-box !important;
}

/* Bottom navigation bar */
.bottom-nav {
  position: absolute !important;
  bottom: 20px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  display: flex !important;
  gap: 30px !important;
  z-index: 2 !important;
  background: rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(10px) !important;
  padding: 10px 20px !important;
  border-radius: 25px !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.bottom-nav .nav-link {
  color: #ffffff !important;
  text-decoration: none !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  padding: 5px 10px !important;
  border-radius: 15px !important;
  transition: all 0.3s ease !important;
  white-space: nowrap !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.bottom-nav .nav-link:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-2px) !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7) !important;
}

/* Ensure nav-links never get white backgrounds from global styles */
:host .bottom-nav .nav-link,
.create-account-container .bottom-nav .nav-link,
.light-mode-only .bottom-nav .nav-link {
  background: transparent !important;
}

:host .bottom-nav .nav-link:hover,
.create-account-container .bottom-nav .nav-link:hover,
.light-mode-only .bottom-nav .nav-link:hover {
  background: rgba(255, 255, 255, 0.2) !important;
}

/* Mobile responsive styles for bottom nav */
@media (max-width: 768px) {
  .bottom-nav {
    display: none !important;
  }
}

@media (max-width: 480px) {
  .bottom-nav {
    display: none !important;
  }
}



/* Responsive design */
@media (max-width: 768px) {
  .create-account-container {
    flex-direction: column;
  }

  .left-section {
    flex: 0 0 100%;
    min-height: 100vh; /* Take full height */
  }

  .right-section {
    display: none !important; /* Force hide right section on mobile */
  }
}

@media (max-width: 480px) {
  .left-section {
    padding: 20px;
  }

  form {
    gap: 12px;
  }

  input,
  button {
    padding: 12px;
    font-size: 14px;
  }
}

/* Dark mode overrides - ensure create account page stays in light mode */
:host {
  /* Force light mode styles regardless of theme */
  color: #333 !important;
}

.container {
  background-color: #ffffff !important;
}

.left-section {
  background-color: #ffffff !important;
  color: #333333 !important;
}

.left-section h1 {
  color: #333333 !important;
}

.left-section p {
  color: #666666 !important;
}

.left-section p a {
  color: #6b48ff !important;
}

.left-section input {
  background-color: #ffffff !important;
  color: #333333 !important;
  border: 1px solid #ccc !important;
}

.left-section input::placeholder {
  color: #999999 !important;
}

.left-section .terms {
  color: #666666 !important;
}

.left-section .terms label a {
  color: #6b48ff !important;
}

.left-section footer {
  color: #666666 !important;
}

/* Fix for Angular form classes in dark mode */
.ng-untouched, .ng-pristine, .ng-invalid, .ng-valid {
  background-color: #ffffff !important;
  color: #333333 !important;
}

/* Success and Error Messages */
.error-message {
  color: #dc3545 !important;
  background-color: #f8d7da !important;
  border: 1px solid #f5c6cb !important;
  border-radius: 8px !important;
  padding: 10px !important;
  font-size: 0.9rem !important;
  margin-top: 10px !important;
  text-align: center !important;
  font-weight: 500 !important;
}

.success-message {
  color: #155724 !important;
  background-color: #d4edda !important;
  border: 1px solid #c3e6cb !important;
  border-radius: 8px !important;
  padding: 10px !important;
  font-size: 0.9rem !important;
  margin-top: 10px !important;
  text-align: center !important;
  font-weight: 500 !important;
}






